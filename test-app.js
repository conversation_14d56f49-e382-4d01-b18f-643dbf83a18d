#!/usr/bin/env node

const { chromium } = require('playwright');

async function testApp() {
  console.log('🚀 开始测试知识卡片应用...\n');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // 1. 测试首页加载
    console.log('📄 测试首页加载...');
    await page.goto('http://localhost:3000');
    await page.waitForSelector('h1:has-text("增强阅读，即刻开始")');
    console.log('✅ 首页加载成功');
    
    // 2. 测试输入框和按钮
    console.log('\n🔍 测试输入功能...');
    const input = page.locator('input[placeholder*="粘贴一篇文章"]');
    const button = page.locator('button:has(svg)').first();
    
    await input.fill('https://example.com');
    console.log('✅ 输入框填写成功');
    
    // 3. 测试提交功能
    console.log('\n📤 测试提交功能...');
    await button.click();
    
    // 等待页面切换到工作台
    await page.waitForSelector('.min-h-screen.bg-background', { timeout: 10000 });
    console.log('✅ 页面切换到工作台');
    
    // 4. 检查组件是否加载
    console.log('\n🧩 检查组件加载...');
    
    // 检查顶部导航
    const topNav = page.locator('[class*="TopNavigation"], nav, header');
    if (await topNav.count() > 0) {
      console.log('✅ 顶部导航组件已加载');
    } else {
      console.log('⚠️  顶部导航组件未找到');
    }
    
    // 检查内容查看器
    const contentViewer = page.locator('.h-full.bg-white, .prose, [class*="prose"]');
    if (await contentViewer.count() > 0) {
      console.log('✅ 内容查看器组件已加载');
    } else {
      console.log('⚠️  内容查看器组件未找到');
    }
    
    // 检查侧边栏
    const sidebar = page.locator('[class*="Sidebar"], [class*="sidebar"], .w-96');
    if (await sidebar.count() > 0) {
      console.log('✅ 侧边栏组件已加载');
    } else {
      console.log('⚠️  侧边栏组件未找到');
    }
    
    // 检查知识卡片标签
    const cardsTab = page.locator('text=知识卡片');
    if (await cardsTab.count() > 0) {
      console.log('✅ 知识卡片标签已加载');
    } else {
      console.log('⚠️  知识卡片标签未找到');
    }
    
    // 检查AI问答标签
    const chatTab = page.locator('text=AI 问答');
    if (await chatTab.count() > 0) {
      console.log('✅ AI问答标签已加载');
    } else {
      console.log('⚠️  AI问答标签未找到');
    }
    
    // 5. 检查样式是否正确应用
    console.log('\n🎨 检查样式应用...');

    // 等待内容加载完成
    await page.waitForTimeout(3000);

    // 检查 prose 类是否存在
    const proseElements = page.locator('.prose');
    if (await proseElements.count() > 0) {
      console.log('✅ Prose 样式类已应用');
    } else {
      console.log('⚠️  Prose 样式类未找到');
    }

    // 检查是否有格式化的内容
    const formattedContent = page.locator('.prose p, .prose h1, .prose h2, .prose h3');
    if (await formattedContent.count() > 0) {
      console.log('✅ 内容已格式化（包含段落或标题）');
    } else {
      console.log('⚠️  内容未格式化');
    }

    // 检查 Tailwind CSS 是否加载
    const styledElements = page.locator('[class*="bg-"], [class*="text-"], [class*="border-"]');
    if (await styledElements.count() > 0) {
      console.log('✅ Tailwind CSS 样式已加载');
    } else {
      console.log('⚠️  Tailwind CSS 样式未加载');
    }

    // 检查图标是否显示
    const icons = page.locator('svg');
    if (await icons.count() > 0) {
      console.log('✅ Lucide React 图标已显示');
    } else {
      console.log('⚠️  图标未显示');
    }

    console.log('\n🎉 测试完成！应用功能和样式检查完毕。');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
  } finally {
    await browser.close();
  }
}

// 运行测试
testApp().catch(console.error);

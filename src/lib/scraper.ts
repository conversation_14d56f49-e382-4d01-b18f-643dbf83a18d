import { Readability } from '@mozilla/readability'
import { JSD<PERSON> } from 'jsdom'
import axios from 'axios'
import { chromium } from 'playwright'

export interface ScrapedContent {
  title: string
  content: string
  textContent: string
  url: string
}

/**
 * 尝试使用轻量级方法抓取网页内容
 */
async function fastScrape(url: string): Promise<ScrapedContent | null> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    })

    const dom = new JSDOM(response.data, { url })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()

    if (article && article.content && article.textContent) {
      return {
        title: article.title || 'Untitled',
        content: article.content,
        textContent: article.textContent,
        url
      }
    }

    return null
  } catch (error) {
    console.error('Fast scrape failed:', error)
    return null
  }
}

/**
 * 使用 Playwright 进行重型抓取（处理 JS 渲染页面）
 */
async function heavyScrape(url: string): Promise<ScrapedContent | null> {
  let browser = null
  try {
    browser = await chromium.launch()
    const page = await browser.newPage()
    
    await page.goto(url, { waitUntil: 'networkidle' })
    
    // 等待页面完全加载
    await page.waitForTimeout(2000)
    
    const content = await page.content()
    const dom = new JSDOM(content, { url })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()

    if (article && article.content && article.textContent) {
      return {
        title: article.title || 'Untitled',
        content: article.content,
        textContent: article.textContent,
        url
      }
    }

    return null
  } catch (error) {
    console.error('Heavy scrape failed:', error)
    return null
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

/**
 * 智能抓取网页内容（先尝试快速方法，失败后使用重型方法）
 */
export async function scrapeWebPage(url: string): Promise<ScrapedContent> {
  // 先尝试快速抓取
  let result = await fastScrape(url)
  
  if (!result) {
    console.log('Fast scrape failed, trying heavy scrape...')
    // 如果快速抓取失败，使用 Playwright
    result = await heavyScrape(url)
  }

  if (!result) {
    throw new Error('Failed to scrape content from the webpage')
  }

  return result
} 
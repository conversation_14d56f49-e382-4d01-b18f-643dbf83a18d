'use client'

import { useState } from 'react'
import { WelcomeScreen } from '@/components/WelcomeScreen'
import { TopNavigation } from '@/components/TopNavigation'
import { ContentViewer } from '@/components/ContentViewer'
import { Sidebar } from '@/components/Sidebar'
import { AIChat } from '@/components/AIChat'

interface KnowledgeCard {
  title: string
  content: string
}

interface ProcessedContent {
  originalContent: string
  knowledgeCards: KnowledgeCard[]
  title: string
  cached?: boolean
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

type ViewMode = 'welcome' | 'workbench' | 'chat'

export default function Home() {
  const [viewMode, setViewMode] = useState<ViewMode>('welcome')
  const [currentUrl, setCurrentUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [processedContent, setProcessedContent] = useState<ProcessedContent | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatLoading, setChatLoading] = useState(false)

  const handleProcessInput = async (input: string) => {
    setCurrentUrl(input)
    setLoading(true)
    setViewMode('workbench')

    try {
      const response = await fetch('/api/process-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: input }),
      })

      if (!response.ok) {
        throw new Error('Failed to process input')
      }

      const result: ProcessedContent = await response.json()
      setProcessedContent(result)
      setChatMessages([]) // 清空聊天记录
    } catch (error) {
      console.error('Error:', error)
      // 可以添加 toast 提示
      alert('处理输入时出错，请检查输入内容是否有效')
      setViewMode('welcome')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveCard = async (card: KnowledgeCard) => {
    try {
      const response = await fetch('/api/save-card', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: card.title,
          content: card.content,
          sourceUrl: currentUrl,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save card')
      }
      // 成功保存，不需要额外操作，组件内部会处理状态更新
    } catch (error) {
      console.error('Error:', error)
      throw error // 重新抛出错误，让组件处理
    }
  }

  const handleSendMessage = async (message: string) => {
    if (!processedContent) return

    setChatLoading(true)
    const userMessage: ChatMessage = { role: 'user', content: message }
    setChatMessages(prev => [...prev, userMessage])

    try {
      const response = await fetch('/api/ask-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: message,
          context: processedContent.originalContent,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get answer')
      }

      const result = await response.json()
      const assistantMessage: ChatMessage = { role: 'assistant', content: result.answer }
      setChatMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error:', error)
      const errorMessage: ChatMessage = { 
        role: 'assistant', 
        content: '抱歉，回答问题时出错了。请稍后再试。' 
      }
      setChatMessages(prev => [...prev, errorMessage])
    } finally {
      setChatLoading(false)
    }
  }

  const handleLogoClick = () => {
    setViewMode('welcome')
    setProcessedContent(null)
    setChatMessages([])
    setCurrentUrl('')
  }

  const handleSyncClick = async () => {
    // 实现同步知识库的逻辑
    // 这里可以调用同步API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟异步操作
  }

  // 欢迎页
  if (viewMode === 'welcome') {
    return (
      <WelcomeScreen
        onSubmit={handleProcessInput}
        loading={loading}
      />
    )
  }

  // 知识库对话页面
  if (viewMode === 'chat') {
    return (
      <div className="min-h-screen bg-background">
        <TopNavigation
          onLogoClick={handleLogoClick}
          onChatClick={() => setViewMode('workbench')}
          onSyncClick={handleSyncClick}
        />
        <div className="h-[calc(100vh-4rem)] max-w-4xl mx-auto p-4">
          <div className="h-full bg-white rounded-lg border border-border">
            <div className="h-full p-6">
              <div className="mb-6">
                <h1 className="text-2xl font-semibold text-foreground">知识库对话</h1>
                <p className="text-muted-foreground mt-2">
                  与您的个人知识库进行智能对话
                </p>
              </div>
              <div className="h-[calc(100%-5rem)]">
                <AIChat
                  messages={chatMessages}
                  loading={chatLoading}
                  onSendMessage={handleSendMessage}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 主工作台 - 三面板布局
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/30 flex relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-indigo-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-tr from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* 左面板：文章标题和导航 */}
      <div className="relative z-10 w-80 bg-white/80 backdrop-blur-xl border-r border-border/50 flex flex-col shadow-soft">
        {/* 顶部导航 */}
        <div className="p-6 border-b border-border/50">
          <button
            onClick={handleLogoClick}
            className="flex items-center space-x-3 w-full text-left hover:bg-primary/5 p-3 rounded-xl transition-all duration-200 group"
          >
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-soft group-hover:shadow-medium transition-all duration-200">
                <span className="text-white font-bold text-sm">知</span>
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
            </div>
            <div>
              <span className="font-bold text-foreground text-lg group-hover:text-primary transition-colors">知识卡片</span>
              <div className="text-xs text-muted-foreground font-medium">AI 智能分析</div>
            </div>
          </button>
        </div>

        {/* 文章标题区域 */}
        <div className="flex-1 p-6">
          {processedContent ? (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-bold text-foreground mb-3 leading-tight">
                  {processedContent.title || '文章标题'}
                </h2>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{currentUrl ? new URL(currentUrl).hostname : '本地内容'}</span>
                </div>
              </div>

              {/* 文章统计信息 */}
              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-primary/5 to-purple-500/5 rounded-xl border border-primary/10">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">知识卡片</span>
                    <span className="text-lg font-bold text-primary">{processedContent.knowledgeCards?.length || 0}</span>
                  </div>
                  <div className="w-full bg-primary/10 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-primary to-purple-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${Math.min((processedContent.knowledgeCards?.length || 0) * 20, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div className="p-4 bg-gradient-to-r from-emerald-500/5 to-teal-500/5 rounded-xl border border-emerald-500/10">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">字数统计</span>
                    <span className="text-lg font-bold text-emerald-600">{(processedContent.originalContent?.length || 0).toLocaleString()}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    约 {Math.ceil((processedContent.originalContent?.length || 0) / 500)} 分钟阅读
                  </div>
                </div>

                {processedContent.cached && (
                  <div className="flex items-center space-x-2 p-3 bg-amber-50 border border-amber-200 rounded-xl">
                    <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-medium text-amber-700">来自缓存</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground mt-12 space-y-4">
              <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-2xl mx-auto flex items-center justify-center">
                <div className="text-2xl animate-pulse">📄</div>
              </div>
              <div>
                <p className="font-medium">正在加载文章...</p>
                <div className="flex items-center justify-center space-x-1 mt-2">
                  <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 中间面板：文章内容 */}
      <div className="relative z-10 flex-1 bg-white/60 backdrop-blur-xl border-r border-border/50 shadow-soft">
        <ContentViewer
          content={processedContent}
          loading={loading}
        />
      </div>

      {/* 右面板：知识卡片和AI聊天 */}
      <div className="relative z-10 w-96 bg-white/80 backdrop-blur-xl flex flex-col shadow-soft">
        <Sidebar
          knowledgeCards={processedContent?.knowledgeCards || []}
          knowledgeCardsLoading={loading}
          chatMessages={chatMessages}
          chatLoading={chatLoading}
          onSaveCard={handleSaveCard}
          onSendMessage={handleSendMessage}
        />
      </div>
    </div>
  )
}

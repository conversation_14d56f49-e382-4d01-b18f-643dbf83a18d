import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-soft hover:shadow-medium hover:from-primary-600 hover:to-primary-700 focus:ring-primary-500/50 active:scale-[0.98]",
        destructive:
          "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-soft hover:shadow-medium hover:from-red-600 hover:to-red-700 focus:ring-red-500/50 active:scale-[0.98]",
        outline:
          "border border-border bg-background shadow-soft hover:bg-accent hover:shadow-medium focus:ring-primary-500/50 active:scale-[0.98]",
        secondary:
          "bg-secondary text-secondary-foreground shadow-soft hover:bg-secondary/80 hover:shadow-medium focus:ring-primary-500/50 active:scale-[0.98]",
        ghost:
          "hover:bg-accent hover:text-accent-foreground focus:ring-primary-500/50 active:scale-[0.98]",
        link: "text-primary underline-offset-4 hover:underline focus:ring-primary-500/50",
        gradient:
          "bg-gradient-to-r from-gradient-from via-gradient-via to-gradient-to text-white shadow-glow hover:shadow-glow-lg focus:ring-primary-500/50 active:scale-[0.98]",
        glass:
          "glass text-foreground shadow-soft hover:shadow-medium backdrop-blur-xl focus:ring-primary-500/50 active:scale-[0.98]",
      },
      size: {
        default: "h-10 px-4 py-2 text-sm",
        sm: "h-8 rounded-lg px-3 text-xs",
        lg: "h-12 rounded-xl px-6 text-base",
        xl: "h-14 rounded-2xl px-8 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8 rounded-lg",
        "icon-lg": "h-12 w-12 rounded-xl",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }

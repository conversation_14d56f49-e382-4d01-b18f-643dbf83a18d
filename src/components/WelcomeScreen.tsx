'use client'

import { useState } from 'react'
import { Send, Paperclip, Sparkles, Globe, FileText, Brain } from 'lucide-react'

interface WelcomeScreenProps {
  onSubmit: (input: string) => void
  loading: boolean
}

export function WelcomeScreen({ onSubmit, loading }: WelcomeScreenProps) {
  const [input, setInput] = useState('')

  const handleSubmit = () => {
    if (input.trim()) {
      onSubmit(input.trim())
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !loading) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 flex items-center justify-between p-6 backdrop-blur-sm">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full animate-pulse"></div>
          </div>
          <div>
            <span className="font-bold text-slate-800 text-lg">知识卡片</span>
            <div className="text-xs text-slate-500 font-medium">AI 驱动的知识管理</div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="px-3 py-1 bg-white/60 backdrop-blur-sm rounded-full border border-white/20">
            <span className="text-xs font-medium text-slate-600">Beta</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex flex-col items-center justify-center px-6">
        <div className="w-full max-w-4xl">
          {/* Welcome Message */}
          <div className="text-center mb-12 animate-fade-in-up">
            <div className="inline-flex items-center space-x-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-white/20 mb-6">
              <Sparkles className="w-4 h-4 text-indigo-500" />
              <span className="text-sm font-medium text-slate-700">AI 智能分析</span>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-slate-800 via-indigo-800 to-purple-800 bg-clip-text text-transparent mb-6 leading-tight">
              将知识转化为
              <br />
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                智慧卡片
              </span>
            </h1>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
              输入网页链接或文章内容，AI 将为您生成结构化的知识卡片，
              <br />
              让复杂信息变得简单易懂
            </p>
          </div>

          {/* Input Area */}
          <div className="relative mb-12 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
              <div className="relative flex items-end bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300">
                <div className="flex-1 min-h-[60px] max-h-[200px]">
                  <textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    disabled={loading}
                    placeholder="输入网页链接或粘贴文章内容，开始您的知识之旅..."
                    className="w-full h-full min-h-[60px] max-h-[200px] px-6 py-4 bg-transparent border-0 resize-none outline-none placeholder:text-slate-400 text-slate-800 text-lg leading-relaxed"
                  />
                </div>

                <div className="flex items-center space-x-3 p-4">
                  <button
                    type="button"
                    className="p-3 text-slate-400 hover:text-slate-600 transition-all duration-200 rounded-xl hover:bg-slate-100/50 group"
                    disabled={loading}
                  >
                    <Paperclip className="w-5 h-5 group-hover:rotate-12 transition-transform duration-200" />
                  </button>

                  <button
                    onClick={handleSubmit}
                    disabled={!input.trim() || loading}
                    className="relative p-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 disabled:from-slate-300 disabled:to-slate-400 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl group overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    {loading ? (
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    ) : (
                      <Send className="w-5 h-5 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-200" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <button
              onClick={() => setInput('https://example.com/article')}
              className="group p-6 text-left bg-white/60 backdrop-blur-sm hover:bg-white/80 rounded-2xl border border-white/20 hover:border-indigo-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              disabled={loading}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Globe className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-slate-800 group-hover:text-indigo-700 transition-colors">网页分析</div>
                  <div className="text-sm text-slate-500">智能提取网页内容</div>
                </div>
              </div>
              <p className="text-sm text-slate-600 leading-relaxed">
                输入网页链接，AI 自动提取并分析文章内容，生成结构化知识卡片
              </p>
            </button>

            <button
              onClick={() => setInput('请帮我分析这篇文章的主要观点...')}
              className="group p-6 text-left bg-white/60 backdrop-blur-sm hover:bg-white/80 rounded-2xl border border-white/20 hover:border-purple-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              disabled={loading}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-slate-800 group-hover:text-purple-700 transition-colors">文本分析</div>
                  <div className="text-sm text-slate-500">深度理解文章内容</div>
                </div>
              </div>
              <p className="text-sm text-slate-600 leading-relaxed">
                直接粘贴文章内容，AI 深度分析文本结构和核心观点
              </p>
            </button>

            <button
              onClick={() => setInput('帮我总结一下机器学习的基本概念')}
              className="group p-6 text-left bg-white/60 backdrop-blur-sm hover:bg-white/80 rounded-2xl border border-white/20 hover:border-emerald-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              disabled={loading}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-slate-800 group-hover:text-emerald-700 transition-colors">知识问答</div>
                  <div className="text-sm text-slate-500">AI 智能回答</div>
                </div>
              </div>
              <p className="text-sm text-slate-600 leading-relaxed">
                提出问题或话题，AI 为您生成相关的知识卡片和解答
              </p>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="relative z-10 p-6 text-center">
        <div className="inline-flex items-center space-x-2 px-4 py-2 bg-white/40 backdrop-blur-sm rounded-full border border-white/20">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-slate-600">
            AI 助手已就绪，随时为您服务
          </span>
        </div>
      </div>
    </div>
  )
}
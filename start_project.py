import subprocess
import os

def run_command(command):
    try:
        subprocess.run(command, check=True, shell=True)
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {e}")
        exit(1)

def main():
    print("Starting project setup...")

    # Check for node_modules and install if not present
    if not os.path.exists("node_modules"):
        print("node_modules not found. Running npm install...")
        run_command("npm install")
    else:
        print("node_modules found. Skipping npm install.")

    # Run Prisma commands
    print("Running npx prisma db push...")
    run_command("npx prisma db push")

    print("Running npx prisma generate...")
    run_command("npx prisma generate")

    # Start the development server
    print("Starting development server with npm run dev...")
    run_command("npm run dev")

if __name__ == "__main__":
    main() 